<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!--
    COMPLETELY DYNAMIC NETWORK SECURITY CONFIGURATION
    No hardcoded IP addresses - allows cleartext traffic for all private networks
    This enables mDNS discovery to work on any WiFi network
    -->

    <!-- Allow cleartext traffic for all private network ranges (RFC 1918) -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Localhost and emulator -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>

        <!-- mDNS .local domains -->
        <domain includeSubdomains="true">*.local</domain>
        <domain includeSubdomains="true">attendance-system.local</domain>
    </domain-config>

    <!--
    CRITICAL: Allow cleartext for ALL private IP ranges
    This enables dynamic discovery on any private network
    -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
