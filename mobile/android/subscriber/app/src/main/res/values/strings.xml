<resources>
    <string name="app_name">Attendance App</string>
    <string name="login_title">Subscriber Login</string>
    <string name="mobile_number">Mobile Number</string>
    <string name="organization_id">Organization ID</string>
    <string name="pin">PIN</string>
    <string name="login">Login</string>
    <string name="send_otp">Send OTP</string>
    <string name="verify_otp">Verify OTP</string>
    <string name="resend_otp">Resend OTP</string>
    <string name="dashboard">Dashboard</string>
    <string name="profile">Profile</string>
    <string name="sessions">Sessions</string>
    <string name="history">History</string>
    <string name="scan_qr">Scan QR</string>
    <string name="logout">Logout</string>
    <string name="welcome_back">Welcome back!</string>
    <string name="quick_actions">Quick Actions</string>
    <string name="active_sessions">Active Sessions</string>
    <string name="recent_attendance">Recent Attendance</string>
    <string name="upcoming_sessions">Upcoming Sessions</string>
    <string name="attendance_history">Attendance History</string>
    <string name="available_sessions">Available Sessions</string>
    <string name="qr_scanner">QR Code Scanner</string>
    <string name="camera_permission_required">Camera Permission Required</string>
    <string name="camera_permission_rationale">To scan QR codes for attendance, we need access to your camera.</string>
    <string name="grant_camera_permission">Grant Camera Permission</string>
    <string name="point_camera_at_qr">Point your camera at a QR code</string>
    <string name="qr_code_instructions">Make sure the QR code is clearly visible and well-lit</string>
    <string name="processing_checkin">Processing check-in...</string>
    <string name="no_active_sessions">No Active Sessions</string>
    <string name="no_active_sessions_message">There are currently no active sessions available for check-in.</string>
    <string name="no_attendance_history">No Attendance History</string>
    <string name="no_attendance_history_message">Your attendance history will appear here once you start checking in to sessions.</string>
    <string name="total_sessions_attended">Total Sessions Attended</string>
    <string name="check_in">Check In</string>
    <string name="available_methods">Available Methods:</string>
    <string name="personal_information">Personal Information</string>
    <string name="account_actions">Account Actions</string>
    <string name="change_pin">Change PIN</string>
    <string name="update_profile">Update Profile</string>
    <string name="first_name">First Name</string>
    <string name="last_name">Last Name</string>
    <string name="email">Email</string>
    <string name="nfc_card">NFC Card</string>
    <string name="assigned">Assigned</string>
    <string name="not_assigned">Not Assigned</string>
    <string name="refresh">Refresh</string>
    <string name="back">Back</string>
    <string name="error_network">Network error occurred</string>
    <string name="error_login_failed">Login failed</string>
    <string name="error_invalid_credentials">Invalid credentials</string>
    <string name="error_otp_expired">OTP has expired</string>
    <string name="success_otp_sent">OTP sent successfully</string>
    <string name="success_login">Login successful</string>
    <string name="success_checkin">Check-in successful</string>
    <string name="success_checkout">Check-out successful</string>
    <string name="done">DONE</string>
    <string name="attendance_recorded">Attendance Recorded</string>
    <string name="check_in_success_message">You have successfully checked in to the session</string>
    <string name="check_out_success_message">You have successfully checked out from the session</string>
    <string name="check_in_failed">Check-in Failed</string>
    <string name="check_out_failed">Check-out Failed</string>
    <string name="try_again">Try Again</string>
    <string name="return_to_dashboard">Return to Dashboard</string>
    <string name="wifi_check_in_title">WiFi Check-In</string>
    <string name="select_network">Select Network</string>
    <string name="connect_to_network">Connect to Network</string>
    <string name="scanning_qr_code">Scanning QR Code...</string>
    <string name="qr_code_detected">QR Code Detected!</string>
    <string name="processing_attendance">Processing Attendance...</string>
</resources>
