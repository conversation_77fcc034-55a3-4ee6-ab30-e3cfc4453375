<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- Local development servers -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>

        <!-- Local network servers -->
        <domain includeSubdomains="true">***********/24</domain>
        <domain includeSubdomains="true">***********/24</domain>
        <domain includeSubdomains="true">************/24</domain>
        <domain includeSubdomains="true">***********/24</domain>

        <!-- Specific server IPs -->
        <domain includeSubdomains="true">************</domain>
        <domain includeSubdomains="true">**************</domain>
        <domain includeSubdomains="true">***********</domain>

        <!-- mDNS hostnames - DYNAMIC DISCOVERY -->
        <domain includeSubdomains="true">*.local</domain>
        <domain includeSubdomains="true">attendance-system.local</domain>
        <domain includeSubdomains="true">restaurant.local</domain>
    </domain-config>

    <!--
    CRITICAL: Allow cleartext for ALL private IP ranges
    This enables dynamic discovery on any private network for entity admin
    -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
