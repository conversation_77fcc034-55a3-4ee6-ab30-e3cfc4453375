<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Theme.EntityAdmin" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/secondary_purple</item>
        <item name="colorSecondaryVariant">@color/secondary_purple_dark</item>
        <item name="colorOnSecondary">@color/text_on_primary</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_elevated</item>
        <item name="colorOnBackground">@color/text_on_surface</item>
        <item name="colorOnSurface">@color/text_on_surface</item>

        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>

        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/primary_blue_dark</item>

        <!-- Text Colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        <!-- Material Design 3 Colors -->
        <item name="colorError">@color/error_red</item>
        <item name="colorOnError">@color/white</item>
    </style>

    <!-- Light Theme Variant -->
    <style name="Theme.EntityAdmin.Light" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>
        <item name="colorSecondary">@color/secondary_purple</item>
        <item name="colorSecondaryVariant">@color/secondary_purple_dark</item>
        <item name="colorOnSecondary">@color/text_on_primary</item>
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_elevated</item>
        <item name="colorOnBackground">@color/text_on_surface</item>
        <item name="colorOnSurface">@color/text_on_surface</item>
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
    </style>
</resources>
