<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:startDestination="@id/loginFragment">

    <!-- Login Flow -->
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.example.entityadmin.ui.LoginFragment"
        android:label="@string/login">
        <action
            android:id="@+id/action_loginFragment_to_mainContainer"
            app:destination="@id/mainContainerFragment"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <!-- Main Container with Bottom Navigation -->
    <fragment
        android:id="@+id/mainContainerFragment"
        android:name="com.example.entityadmin.ui.MainContainerFragment"
        android:label="Entity Admin" />



</navigation>
