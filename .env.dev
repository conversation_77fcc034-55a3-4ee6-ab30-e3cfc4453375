# Development Environment Configuration for Attendance Management System
# Copy this file to .env for development environment

# Database Configuration
POSTGRES_PASSWORD=0000

# Application Security
JWT_SECRET=ams_development_secret_key_2024_not_for_production

# Redis Configuration
REDIS_PASSWORD=redis_dev_2024

# Monitoring
GRAFANA_PASSWORD=admin123

# Application Configuration
REGION=local
APP_BASE_URL=http://localhost:3002

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_MAX_SIZE=10m
LOG_MAX_FILES=3

# Performance Tuning (Development)
JAVA_HEAP_SIZE=1g
POSTGRES_SHARED_BUFFERS=128MB
POSTGRES_EFFECTIVE_CACHE_SIZE=512MB

# Network Configuration
NETWORK_SUBNET=**********/16

# Service Discovery
SERVICE_DISCOVERY_ENABLED=true
SERVICE_DISCOVERY_ENVIRONMENT=development
