# Docker ignore file for Attendance Management System
# Excludes unnecessary files from Docker build context

# Version control
.git
.gitignore
.gitattributes

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.dev
.env.prod

# Build outputs
target/
build/
dist/

# Maven
.mvn/wrapper/maven-wrapper.jar
.mvn/wrapper/maven-wrapper.properties

# Gradle
.gradle/
gradle/

# Android
*.apk
*.aab
local.properties

# iOS
*.xcworkspace
*.xcuserdata

# Documentation
docs/
README.md
TODO_LIST.md

# Docker files (except Dockerfile)
docker-compose*.yml
.dockerignore

# Scripts
scripts/

# Test files
src/test/
**/*test*
**/*spec*

# Uploads and temporary files
uploads/
temp/
tmp/

# Database files
*.db
*.sqlite

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
