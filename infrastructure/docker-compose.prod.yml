# Production Docker Compose for Attendance Management System
# Enhanced configuration with production optimizations

version: '3.8'

services:
  # PostgreSQL Database with production settings
  postgres:
    image: postgres:15-alpine
    container_name: ams-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: attendance_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - ams-network-prod
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d attendance_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Attendance Management System Backend - Production
  ams-backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: ams-backend:prod
    container_name: ams-backend-prod
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Database Configuration
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Application Configuration
      SPRING_PROFILES_ACTIVE: docker,prod
      SERVER_PORT: 8080
      GRPC_SERVER_PORT: 9090
      
      # Service Discovery
      SERVICE_DISCOVERY_ENABLED: true
      SERVICE_DISCOVERY_SERVICE_NAME: attendance-system
      SERVICE_DISCOVERY_ENVIRONMENT: production
      SERVICE_DISCOVERY_REGION: ${REGION:-us-east-1}
      
      # Observability
      MANAGEMENT_TRACING_SAMPLING_PROBABILITY: 0.05
      MANAGEMENT_ZIPKIN_TRACING_ENDPOINT: http://zipkin:9411/api/v2/spans
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      
      # Security
      JWT_SECRET: ${JWT_SECRET}
      
      # Performance
      JAVA_OPTS: "-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/"
      
      # Logging
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_COM_EXAMPLE_ATTENDANCESYSTEM: INFO
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ams_logs_prod:/app/logs
      - ams_uploads_prod:/app/uploads
      - ams_heapdumps:/app/heapdumps
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "/app/healthcheck.sh"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2.5G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  # Redis for caching (production addition)
  redis:
    image: redis:7-alpine
    container_name: ams-redis-prod
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_2024}
    volumes:
      - redis_data_prod:/data
    ports:
      - "6379:6379"
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Admin Panel (React) - Production
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    image: ams-admin-panel:prod
    container_name: ams-admin-panel-prod
    restart: always
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: production
    ports:
      - "3001:80"
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Entity Dashboard (React) - Production
  entity-dashboard:
    build:
      context: ./entity-dashboard
      dockerfile: Dockerfile
    image: ams-entity-dashboard:prod
    container_name: ams-entity-dashboard-prod
    restart: always
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: production
    ports:
      - "3002:80"
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Public Menu (React) - Production
  public-menu:
    build:
      context: ./public-menu
      dockerfile: Dockerfile
    image: ams-public-menu:prod
    container_name: ams-public-menu-prod
    restart: always
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: production
    ports:
      - "3003:80"
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Load Balancer (for multiple backend instances)
  nginx-lb:
    image: nginx:alpine
    container_name: ams-nginx-lb-prod
    restart: always
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - ams-network-prod
    depends_on:
      - ams-backend
      - admin-panel
      - entity-dashboard
      - public-menu
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: ams-zipkin-prod
    restart: always
    environment:
      STORAGE_TYPE: elasticsearch
      ES_HOSTS: http://elasticsearch:9200
    ports:
      - "9411:9411"
    networks:
      - ams-network-prod
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9411/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Elasticsearch for Zipkin storage
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ams-elasticsearch-prod
    restart: always
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data_prod:/usr/share/elasticsearch/data
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: ams-prometheus-prod
    restart: always
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=20GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./docker/prometheus/prometheus.prod.yml:/etc/prometheus/prometheus.yml
      - ./docker/prometheus/rules:/etc/prometheus/rules
      - prometheus_data_prod:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ams-network-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Grafana for Monitoring Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: ams-grafana-prod
    restart: always
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SECURITY_ALLOW_EMBEDDING: true
      GF_AUTH_ANONYMOUS_ENABLED: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - ams-network-prod
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

# Networks
networks:
  ams-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: ams-prod-br0

# Volumes
volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  elasticsearch_data_prod:
    driver: local
  ams_logs_prod:
    driver: local
  ams_uploads_prod:
    driver: local
  ams_heapdumps:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local
