# Docker Compose for Attendance Management System
# Complete microservices deployment with observability stack

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ams-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: attendance_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-attendance_secure_2024}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d attendance_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Attendance Management System Backend
  ams-backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: ams-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Database Configuration
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-attendance_secure_2024}
      
      # Application Configuration
      SPRING_PROFILES_ACTIVE: docker,prod
      SERVER_PORT: 8080
      GRPC_SERVER_PORT: 9090
      
      # Service Discovery
      SERVICE_DISCOVERY_ENABLED: true
      SERVICE_DISCOVERY_SERVICE_NAME: attendance-system
      SERVICE_DISCOVERY_ENVIRONMENT: docker
      SERVICE_DISCOVERY_REGION: local
      
      # Observability
      MANAGEMENT_TRACING_SAMPLING_PROBABILITY: 0.1
      MANAGEMENT_ZIPKIN_TRACING_ENDPOINT: http://zipkin:9411/api/v2/spans
      
      # Security
      JWT_SECRET: ${JWT_SECRET:-ams_production_secret_key_2024_very_secure}
      
      # Resource Configuration
      JAVA_OPTS: "-Xmx1g -Xms512m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ams_logs:/app/logs
      - ams_uploads:/app/uploads
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "/app/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Admin Panel (React)
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: ams-admin-panel
    restart: unless-stopped
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: docker
    ports:
      - "3001:80"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Entity Dashboard (React)
  entity-dashboard:
    build:
      context: ./entity-dashboard
      dockerfile: Dockerfile
    container_name: ams-entity-dashboard
    restart: unless-stopped
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: docker
    ports:
      - "3002:80"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Public Menu (React)
  public-menu:
    build:
      context: ./public-menu
      dockerfile: Dockerfile
    container_name: ams-public-menu
    restart: unless-stopped
    depends_on:
      ams-backend:
        condition: service_healthy
    environment:
      REACT_APP_API_BASE_URL: http://ams-backend:8080
      REACT_APP_ENVIRONMENT: docker
    ports:
      - "3003:80"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Zipkin for Distributed Tracing
  zipkin:
    image: openzipkin/zipkin:latest
    container_name: ams-zipkin
    restart: unless-stopped
    environment:
      STORAGE_TYPE: mem
    ports:
      - "9411:9411"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9411/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: ams-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Grafana for Monitoring Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: ams-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - ams-network
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

# Networks
networks:
  ams-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  ams_logs:
    driver: local
  ams_uploads:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
