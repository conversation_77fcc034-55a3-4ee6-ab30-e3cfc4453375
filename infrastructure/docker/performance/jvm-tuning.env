# JVM Performance Tuning for Microservices
# These environment variables optimize JVM performance for containerized microservices

# Common JVM Options for all services
JAVA_OPTS_COMMON=-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat

# Service-specific JVM tuning based on expected load

# Auth Service - High concurrency, frequent JWT operations
AUTH_SERVICE_JAVA_OPTS=-Xmx512m -Xms256m -XX:NewRatio=2 -XX:+UseBiasedLocking -XX:BiasedLockingStartupDelay=0

# Organization Service - Medium load, complex queries
ORGANIZATION_SERVICE_JAVA_OPTS=-Xmx512m -Xms256m -XX:NewRatio=3 -XX:+UseCompressedOops

# Subscriber Service - High read operations, mobile clients
SUBSCRIBER_SERVICE_JAVA_OPTS=-Xmx768m -Xms384m -XX:NewRatio=2 -XX:+AggressiveOpts

# Attendance Service - Heavy processing, face recognition
ATTENDANCE_SERVICE_JAVA_OPTS=-Xmx1024m -Xms512m -XX:NewRatio=1 -XX:+UseParallelGC -XX:ParallelGCThreads=4

# Menu Service - Read-heavy, caching
MENU_SERVICE_JAVA_OPTS=-Xmx384m -Xms192m -XX:NewRatio=4 -XX:+UseCompressedOops

# Order Service - Transaction processing
ORDER_SERVICE_JAVA_OPTS=-Xmx512m -Xms256m -XX:NewRatio=2 -XX:+UseBiasedLocking

# Table Service - Light load
TABLE_SERVICE_JAVA_OPTS=-Xmx256m -Xms128m -XX:NewRatio=4 -XX:+UseCompressedOops

# API Gateway - High throughput, low latency
API_GATEWAY_JAVA_OPTS=-Xmx512m -Xms256m -XX:NewRatio=1 -XX:+UseParallelGC -XX:+AggressiveOpts

# GC Tuning for high-throughput services
GC_OPTS_HIGH_THROUGHPUT=-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m -XX:+G1UseAdaptiveIHOP

# GC Tuning for low-latency services  
GC_OPTS_LOW_LATENCY=-XX:+UseZGC -XX:+UnlockExperimentalVMOptions -XX:+UseTransparentHugePages

# Monitoring and Debugging Options
MONITORING_OPTS=-XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/

# Development vs Production
DEVELOPMENT_OPTS=-XX:+PrintCompilation -XX:+TraceClassLoading -XX:+LogVMOutput
PRODUCTION_OPTS=-XX:+DisableExplicitGC -XX:+UseCompressedClassPointers

# Network and I/O tuning
NETWORK_OPTS=-Djava.net.preferIPv4Stack=true -Dnetworkaddress.cache.ttl=60

# Security options
SECURITY_OPTS=-Djava.security.egd=file:/dev/./urandom

# Complete optimized configurations per service
AUTH_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${AUTH_SERVICE_JAVA_OPTS} ${GC_OPTS_LOW_LATENCY} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
ORGANIZATION_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${ORGANIZATION_SERVICE_JAVA_OPTS} ${GC_OPTS_HIGH_THROUGHPUT} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
SUBSCRIBER_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${SUBSCRIBER_SERVICE_JAVA_OPTS} ${GC_OPTS_HIGH_THROUGHPUT} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
ATTENDANCE_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${ATTENDANCE_SERVICE_JAVA_OPTS} ${GC_OPTS_HIGH_THROUGHPUT} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
MENU_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${MENU_SERVICE_JAVA_OPTS} ${GC_OPTS_HIGH_THROUGHPUT} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
ORDER_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${ORDER_SERVICE_JAVA_OPTS} ${GC_OPTS_LOW_LATENCY} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
TABLE_SERVICE_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${TABLE_SERVICE_JAVA_OPTS} ${GC_OPTS_HIGH_THROUGHPUT} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
API_GATEWAY_COMPLETE_OPTS=${JAVA_OPTS_COMMON} ${API_GATEWAY_JAVA_OPTS} ${GC_OPTS_LOW_LATENCY} ${MONITORING_OPTS} ${NETWORK_OPTS} ${SECURITY_OPTS}
