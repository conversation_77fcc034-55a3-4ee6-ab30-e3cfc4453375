# Individual Service Docker Compose
# Use this to run individual services with Docker
# Usage: docker-compose -f docker-compose.individual.yml up <service-name>

version: '3.8'

networks:
  ams-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local

services:
  # Infrastructure Services
  postgres:
    image: postgres:15-alpine
    container_name: ams-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: attendance_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init/01-init-database.sql:/docker-entrypoint-initdb.d/01-init-database.sql:ro
    networks:
      - ams-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d attendance_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: ams-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - ams-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Microservices (each can be run individually)
  auth-service:
    build:
      context: ../backend/microservices/auth-service
      dockerfile: Dockerfile.simple
    image: ams-auth-service:latest
    container_name: ams-auth-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8081
      GRPC_SERVER_PORT: 9091
    ports:
      - "8081:8081"
      - "9091:9091"
    networks:
      - ams-network
    profiles:
      - auth-service
      - all-services

  organization-service:
    build:
      context: ../backend/microservices/organization-service
      dockerfile: Dockerfile.simple
    image: ams-organization-service:latest
    container_name: ams-organization-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8082
      GRPC_SERVER_PORT: 9092
    ports:
      - "8082:8082"
      - "9092:9092"
    networks:
      - ams-network
    profiles:
      - organization-service
      - all-services

  subscriber-service:
    build:
      context: ../backend/microservices/subscriber-service
      dockerfile: Dockerfile.simple
    image: ams-subscriber-service:latest
    container_name: ams-subscriber-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8083
      GRPC_SERVER_PORT: 9093
    ports:
      - "8083:8083"
      - "9093:9093"
    networks:
      - ams-network
    profiles:
      - subscriber-service
      - all-services

  attendance-service:
    build:
      context: ../backend/microservices/attendance-service
      dockerfile: Dockerfile.simple
    image: ams-attendance-service:latest
    container_name: ams-attendance-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8084
      GRPC_SERVER_PORT: 9094
    ports:
      - "8084:8084"
      - "9094:9094"
    networks:
      - ams-network
    profiles:
      - attendance-service
      - all-services

  menu-service:
    build:
      context: ../backend/microservices/menu-service
      dockerfile: Dockerfile.simple
    image: ams-menu-service:latest
    container_name: ams-menu-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8085
      GRPC_SERVER_PORT: 9095
    ports:
      - "8085:8085"
      - "9095:9095"
    networks:
      - ams-network
    profiles:
      - menu-service
      - all-services

  order-service:
    build:
      context: ../backend/microservices/order-service
      dockerfile: Dockerfile.simple
    image: ams-order-service:latest
    container_name: ams-order-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8086
      GRPC_SERVER_PORT: 9096
    ports:
      - "8086:8086"
      - "9096:9096"
    networks:
      - ams-network
    profiles:
      - order-service
      - all-services

  table-service:
    build:
      context: ../backend/microservices/table-service
      dockerfile: Dockerfile.simple
    image: ams-table-service:latest
    container_name: ams-table-service
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JDBC_DATABASE_URL: *********************************************
      JDBC_DATABASE_USERNAME: postgres
      JDBC_DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-0000}
      SERVER_PORT: 8087
      GRPC_SERVER_PORT: 9097
    ports:
      - "8087:8087"
      - "9097:9097"
    networks:
      - ams-network
    profiles:
      - table-service
      - all-services

  api-gateway:
    build:
      context: ../backend/microservices/api-gateway
      dockerfile: Dockerfile.simple
    image: ams-api-gateway:latest
    container_name: ams-api-gateway
    restart: unless-stopped
    depends_on:
      - auth-service
      - organization-service
      - subscriber-service
      - attendance-service
      - menu-service
      - order-service
      - table-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
    ports:
      - "8080:8080"
    networks:
      - ams-network
    profiles:
      - api-gateway
      - all-services
