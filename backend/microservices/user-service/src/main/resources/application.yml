# User Service Configuration
spring:
  application:
    name: user-service
  
  # Database Configuration
  datasource:
    url: ${JDBC_DATABASE_URL:**********************************************}
    username: ${JDBC_DATABASE_USERNAME:postgres}
    password: ${JDBC_DATABASE_PASSWORD:0000}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    open-in-view: false
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Asia/Kolkata

# Server Configuration
server:
  port: ${SERVER_PORT:8083}
  address: 0.0.0.0
  servlet:
    context-path: /user

# gRPC Configuration
grpc:
  server:
    port: ${GRPC_SERVER_PORT:9093}
    address: 0.0.0.0
  client:
    GLOBAL:
      negotiation-type: plaintext
    auth-service:
      address: ${AUTH_SERVICE_GRPC_HOST:localhost}:${AUTH_SERVICE_GRPC_PORT:9091}
      negotiation-type: plaintext

# Service Discovery Configuration
service:
  discovery:
    enabled: ${SERVICE_DISCOVERY_ENABLED:true}
    service-name: user-service
    service-type: _grpc._tcp.local.
    port: ${grpc.server.port}
    domain: local.
    ttl: 120
    auto-register: true

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 0.1
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# Logging Configuration
logging:
  level:
    com.example.attendancesystem.user: ${LOG_LEVEL:DEBUG}
    org.springframework.web: INFO
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/user-service.log
    max-size: 10MB
    max-history: 30

# Custom Application Properties
app:
  name: "User Service"
  version: "1.0.0"
  description: "User Management Microservice (SuperAdmin, EntityAdmin, Members)"
