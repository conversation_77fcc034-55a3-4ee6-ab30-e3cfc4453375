# API Gateway Configuration

# JWT Configuration
jwt:
  secret: mySecret<PERSON>ey

spring:
  application:
    name: api-gateway

  # Exclude auto-configuration for database
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  
  # Cloud Gateway Configuration
  cloud:
    gateway:
      discovery:
        locator:
          enabled: false
      routes:
        # Auth Service Routes - Super Admin
        - id: auth-service-super
          uri: http://ams-auth-service:8081
          predicates:
            - Path=/api/auth/super/**
          filters:
            - RewritePath=/api/auth/super/(?<segment>.*), /auth/super/auth/$\{segment}

        # SuperAdmin Routes - Organization Service (Entities)
        - id: super-organization-service-entities
          uri: http://ams-organization-service:8082
          predicates:
            - Path=/api/super/entities
          filters:
            - RewritePath=/api/super/entities, /organization/super/entities

        # SuperAdmin Routes - Organization Service (System Metrics)
        - id: super-organization-service-metrics
          uri: http://ams-organization-service:8082
          predicates:
            - Path=/api/super/system-metrics
          filters:
            - RewritePath=/api/super/system-metrics, /organization/super/system-metrics

        # SuperAdmin Routes - User Service (Entity Admins)
        - id: super-user-service-entity-admins
          uri: http://ams-user-service:8083
          predicates:
            - Path=/api/super/entity-admins
          filters:
            - RewritePath=/api/super/entity-admins, /user/api/users/entity-admins

        # SuperAdmin Routes - User Service (Super Admins)
        - id: super-user-service-super-admins
          uri: http://ams-user-service:8083
          predicates:
            - Path=/api/super/super-admins
          filters:
            - RewritePath=/api/super/super-admins, /user/api/users/super-admins

        # SuperAdmin Routes - Placeholder for NFC Cards (temporary)
        - id: super-placeholder-nfc-cards
          uri: http://ams-organization-service:8082
          predicates:
            - Path=/api/super/nfc-cards
          filters:
            - RewritePath=/api/super/nfc-cards, /organization/super/placeholder/nfc-cards

        # SuperAdmin Routes - Placeholder for Recent Activity (temporary)
        - id: super-placeholder-recent-activity
          uri: http://ams-organization-service:8082
          predicates:
            - Path=/api/super/recent-activity
          filters:
            - RewritePath=/api/super/recent-activity, /organization/super/placeholder/recent-activity

        # Auth Service Routes - Subscriber Auth
        - id: auth-service-subscriber
          uri: http://ams-auth-service:8081
          predicates:
            - Path=/api/auth/subscriber/**
          filters:
            - RewritePath=/api/auth/subscriber/(?<segment>.*), /auth/api/subscriber/auth/$\{segment}

        # Auth Service Routes - Modern Auth (v2)
        - id: auth-service-v2
          uri: http://ams-auth-service:8081
          predicates:
            - Path=/api/auth/v2/**
          filters:
            - RewritePath=/api/auth/v2/(?<segment>.*), /auth/api/v2/auth/$\{segment}

        # Auth Service Routes - Entity Admin (must be last to avoid conflicts)
        - id: auth-service
          uri: http://ams-auth-service:8081
          predicates:
            - Path=/api/auth/**
          filters:
            - RewritePath=/api/auth/(?<segment>.*), /auth/api/auth/$\{segment}

        # Organization Service Routes
        - id: organization-service
          uri: http://ams-organization-service:8082
          predicates:
            - Path=/api/organization/**
          filters:
            - StripPrefix=1
            - RewritePath=/organization/(?<segment>.*), /organization/$\{segment}

        # User Service Routes
        - id: user-service
          uri: http://ams-user-service:8083
          predicates:
            - Path=/api/users/**
          filters:
            - RewritePath=/api/users/(?<segment>.*), /user/api/users/$\{segment}

        # Subscriber Service Routes (Legacy - points to User Service)
        - id: subscriber-service
          uri: http://ams-user-service:8083
          predicates:
            - Path=/api/subscriber/**
          filters:
            - StripPrefix=1
            - RewritePath=/subscriber/(?<segment>.*), /subscriber/$\{segment}

        # Attendance Service Routes
        - id: attendance-service
          uri: http://ams-attendance-service:8084
          predicates:
            - Path=/api/attendance/**
          filters:
            - StripPrefix=1
            - RewritePath=/attendance/(?<segment>.*), /attendance/$\{segment}
        
        # Menu Service Routes
        - id: menu-service
          uri: http://ams-menu-service:8085/menu
          predicates:
            - Path=/api/menu/**
          filters:
            - StripPrefix=2

        # Order Service Routes
        - id: order-service
          uri: http://ams-order-service:8086/order
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2

        # Table Service Routes
        - id: table-service
          uri: http://ams-table-service:8087/table
          predicates:
            - Path=/api/table/**
          filters:
            - StripPrefix=2
      

      
      # Default filters
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
        - AddResponseHeader=X-Response-Default-Foo, Default-Bar

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  address: 0.0.0.0

# Service Discovery Configuration
service:
  discovery:
    enabled: ${SERVICE_DISCOVERY_ENABLED:true}
    service-name: api-gateway
    service-type: _http._tcp.local.
    port: ${server.port}
    domain: local.
    ttl: 120
    auto-register: true

# Security Configuration
security:
  jwt:
    secret: ${JWT_SECRET:api_gateway_secret_key_2024}
    expiration: 3600000

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      default:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
  
  retry:
    instances:
      default:
        max-attempts: 3
        wait-duration: 1s
  
  timelimiter:
    instances:
      default:
        timeout-duration: 10s

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  health:
    zipkin:
      enabled: false
  endpoint:
    health:
      show-details: when-authorized
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 0.1
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}
      connect-timeout: 1s
      read-timeout: 10s

# Logging Configuration
logging:
  level:
    root: INFO
    com.example.attendancesystem.gateway: ${LOG_LEVEL:DEBUG}
    org.springframework.cloud.gateway: DEBUG
    org.springframework.cloud.gateway.route: DEBUG
    org.springframework.cloud.gateway.handler: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/api-gateway.log
    max-size: 10MB
    max-history: 30

# Custom Application Properties
app:
  name: "API Gateway"
  version: "1.0.0"
  description: "API Gateway for Microservices"
  mdns:
    enabled: true
    hostname: restaurant.local
