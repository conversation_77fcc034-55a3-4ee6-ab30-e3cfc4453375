# Auth Service Configuration
spring:
  application:
    name: auth-service
  
  # Database Configuration
  datasource:
    url: ${JDBC_DATABASE_URL:***************************************************************}
    username: ${JD<PERSON>_DATABASE_USERNAME:postgres}
    password: ${JDBC_DATABASE_PASSWORD:0000}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Asia/Kolkata
    date-format: yyyy-MM-dd HH:mm:ss

# Server Configuration
server:
  port: ${SERVER_PORT:8081}
  address: 0.0.0.0
  servlet:
    context-path: /auth

# gRPC Configuration
grpc:
  server:
    port: ${GRPC_SERVER_PORT:9091}
    address: 0.0.0.0
  client:
    GLOBAL:
      negotiation-type: plaintext
    organization-service:
      address: ${ORGANIZATION_SERVICE_HOST:localhost}
      port: ${ORGANIZATION_SERVICE_GRPC_PORT:9092}
      negotiation-type: plaintext
    user-service:
      address: ${USER_SERVICE_HOST:localhost}
      port: ${USER_SERVICE_GRPC_PORT:9093}
      negotiation-type: plaintext
    attendance-service:
      address: ${ATTENDANCE_SERVICE_HOST:localhost}
      port: ${ATTENDANCE_SERVICE_GRPC_PORT:9094}
      negotiation-type: plaintext

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:r3Orv57Tzaglnq8x0YfsRYjmVXHMB4rl9xmY92VbGfSsscQ/jWYAX8IpwIA7ukZPla+lL3msCVbg04u8kyVROw==}
  access-token-expiration: 3600000  # 1 hour in milliseconds
  refresh-token-expiration: *********  # 7 days in milliseconds

  # Subscriber JWT Configuration
  subscriber:
    secret: ${JWT_SUBSCRIBER_SECRET:oJYwEwtBLnDa/xxk4cEatqGBEBOUlnRwW7HhV61AJGVnajTb1BMlbhr1vrRvxd+xbsKtgRXKrlMpNsdHaunIWw==}
    expiration: ${JWT_SUBSCRIBER_EXPIRATION:86400}  # 24 hours
    refresh:
      expiration: ${JWT_SUBSCRIBER_REFRESH_EXPIRATION:604800}  # 7 days

# Security Configuration
security:
  account-lockout:
    max-failed-attempts: 5
    lockout-duration-minutes: 30
  password:
    min-length: 8
    require-uppercase: true
    require-lowercase: true
    require-numbers: true
    require-special-chars: false

# Service Discovery Configuration
service:
  discovery:
    enabled: ${SERVICE_DISCOVERY_ENABLED:true}
    service-name: ${SERVICE_DISCOVERY_SERVICE_NAME:auth-service}
    service-type: _grpc._tcp.local.
    port: ${grpc.server.port}
    weight: 0
    priority: 0
    domain: local.
    ttl: 120
    auto-register: true
    discovery-interval: 30
    version: 1.0.0
    environment: ${SERVICE_DISCOVERY_ENVIRONMENT:development}
    region: ${SERVICE_DISCOVERY_REGION:local}
    network-interface: auto
    use-ipv4: true
    use-ipv6: false

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  health:
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: ${MANAGEMENT_TRACING_SAMPLING_PROBABILITY:0.1}
  zipkin:
    tracing:
      endpoint: ${MANAGEMENT_ZIPKIN_TRACING_ENDPOINT:http://localhost:9411/api/v2/spans}

# Logging Configuration
logging:
  level:
    com.example.attendancesystem.auth: ${LOG_LEVEL:DEBUG}
    org.springframework.security: WARN
    org.springframework.web: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/auth-service.log
    max-size: 10MB
    max-history: 30

# Custom Application Properties
app:
  name: "Attendance Management System - Auth Service"
  version: "1.0.0"
  description: "Authentication and Authorization Microservice"
  
  # Token cleanup configuration
  token-cleanup:
    enabled: true
    interval: 3600000  # 1 hour in milliseconds
    batch-size: 1000
  
  # Rate limiting
  rate-limit:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100

# Network Discovery Configuration
discovery:
  broadcast:
    port: 8888
  service:
    name: AMS-AUTH-SERVICE


