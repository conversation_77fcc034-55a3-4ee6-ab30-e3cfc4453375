# Attendance Service Configuration
spring:
  application:
    name: attendance-service
  
  # Database Configuration
  datasource:
    url: ${JDBC_DATABASE_URL:**********************************************}
    username: ${JDBC_DATABASE_USERNAME:postgres}
    password: ${JDBC_DATABASE_PASSWORD:0000}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    open-in-view: false
  
  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Asia/Kolkata

# Server Configuration
server:
  port: ${SERVER_PORT:8084}
  address: 0.0.0.0
  servlet:
    context-path: /attendance

# gRPC Configuration
grpc:
  server:
    port: ${GRPC_SERVER_PORT:9094}
    address: 0.0.0.0
  client:
    GLOBAL:
      negotiation-type: plaintext
    # Inter-service communication
    user-service:
      host: ${USER_SERVICE_HOST:localhost}
      port: ${USER_SERVICE_GRPC_PORT:9093}
    organization-service:
      host: ${ORGANIZATION_SERVICE_HOST:localhost}
      port: ${ORGANIZATION_SERVICE_GRPC_PORT:9092}

# Service Discovery Configuration
service:
  discovery:
    enabled: ${SERVICE_DISCOVERY_ENABLED:true}
    service-name: attendance-service
    service-type: _grpc._tcp.local.
    port: ${grpc.server.port}
    domain: local.
    ttl: 120
    auto-register: true

# Face Recognition Configuration
face-recognition:
  enabled: ${FACE_RECOGNITION_ENABLED:true}
  model-path: ${FACE_RECOGNITION_MODEL_PATH:models/}
  confidence-threshold: ${FACE_RECOGNITION_CONFIDENCE:0.8}
  max-faces: ${FACE_RECOGNITION_MAX_FACES:10}

# QR Code Configuration
qr-code:
  base-url: ${QR_CODE_BASE_URL:http://localhost:8084/attendance}
  expiry-minutes: ${QR_CODE_EXPIRY:30}

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    grpc:
      enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 0.1
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://localhost:9411/api/v2/spans}

# Logging Configuration
logging:
  level:
    com.example.attendancesystem.attendance: ${LOG_LEVEL:DEBUG}
    org.springframework.web: INFO
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/attendance-service.log
    max-size: 10MB
    max-history: 30

# Custom Application Properties
app:
  name: "Attendance Service"
  version: "1.0.0"
  description: "Attendance Tracking Microservice"
