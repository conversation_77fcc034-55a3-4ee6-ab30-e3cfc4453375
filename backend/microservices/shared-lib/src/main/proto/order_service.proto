syntax = "proto3";

package com.example.attendancesystem.grpc.order;

option java_multiple_files = true;
option java_package = "com.example.attendancesystem.grpc.order";
option java_outer_classname = "OrderServiceProto";

// Order Management Service
service OrderService {
  // Order Operations
  rpc CreateOrder(CreateOrderRequest) returns (OrderResponse);
  rpc GetOrder(GetOrderRequest) returns (OrderResponse);
  rpc UpdateOrder(UpdateOrderRequest) returns (OrderResponse);
  rpc CancelOrder(CancelOrderRequest) returns (OrderResponse);
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
  
  // Order Status Management
  rpc UpdateOrderStatus(UpdateOrderStatusRequest) returns (OrderResponse);
  rpc GetOrdersByStatus(GetOrdersByStatusRequest) returns (ListOrdersResponse);
  
  // Order Item Management
  rpc AddOrderItem(AddOrderItemRequest) returns (OrderItemResponse);
  rpc UpdateOrderItem(UpdateOrderItemRequest) returns (OrderItemResponse);
  rpc RemoveOrderItem(RemoveOrderItemRequest) returns (DeleteResponse);
  
  // Order Analytics
  rpc GetOrderStatistics(GetOrderStatisticsRequest) returns (OrderStatisticsResponse);
  rpc GetPopularItems(GetPopularItemsRequest) returns (PopularItemsResponse);
}

// Order Messages
message Order {
  int64 id = 1;
  string order_number = 2;
  int64 table_id = 3;
  int64 organization_id = 4;
  string customer_name = 5;
  string customer_phone = 6;
  string status = 7; // "PENDING", "CONFIRMED", "PREPARING", "READY", "SERVED", "CANCELLED"
  double total_amount = 8;
  double tax_amount = 9;
  double discount_amount = 10;
  string special_instructions = 11;
  string created_at = 12;
  string updated_at = 13;
  string estimated_ready_time = 14;
  repeated OrderItem order_items = 15;
  RestaurantTable table = 16;
}

message CreateOrderRequest {
  int64 table_id = 1;
  int64 organization_id = 2;
  string customer_name = 3;
  string customer_phone = 4;
  repeated CreateOrderItemRequest order_items = 5;
  string special_instructions = 6;
  double discount_amount = 7;
}

message GetOrderRequest {
  int64 id = 1;
}

message UpdateOrderRequest {
  int64 id = 1;
  string customer_name = 2;
  string customer_phone = 3;
  string special_instructions = 4;
  double discount_amount = 5;
}

message CancelOrderRequest {
  int64 id = 1;
  string cancellation_reason = 2;
}

message ListOrdersRequest {
  int64 organization_id = 1;
  int32 page = 2;
  int32 size = 3;
  string search = 4;
  string status = 5; // Optional filter by status
  int64 table_id = 6; // Optional filter by table
  string start_date = 7;
  string end_date = 8;
}

// Order Status Messages
message UpdateOrderStatusRequest {
  int64 id = 1;
  string status = 2;
  string estimated_ready_time = 3; // Optional
}

message GetOrdersByStatusRequest {
  int64 organization_id = 1;
  string status = 2;
  int32 page = 3;
  int32 size = 4;
}

// Order Item Messages
message OrderItem {
  int64 id = 1;
  int64 order_id = 2;
  int64 item_id = 3;
  int32 quantity = 4;
  double unit_price = 5;
  double total_price = 6;
  string special_instructions = 7;
  string created_at = 8;
  Item item = 9;
}

message CreateOrderItemRequest {
  int64 item_id = 1;
  int32 quantity = 2;
  string special_instructions = 3;
}

message AddOrderItemRequest {
  int64 order_id = 1;
  int64 item_id = 2;
  int32 quantity = 3;
  string special_instructions = 4;
}

message UpdateOrderItemRequest {
  int64 id = 1;
  int32 quantity = 2;
  string special_instructions = 3;
}

message RemoveOrderItemRequest {
  int64 id = 1;
}

// Table Messages (simplified for order context)
message RestaurantTable {
  int64 id = 1;
  string table_number = 2;
  int32 capacity = 3;
  string location = 4;
  bool active = 5;
}

// Item Messages (simplified for order context)
message Item {
  int64 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  string image_url = 5;
  int32 preparation_time = 6;
}

// Analytics Messages
message GetOrderStatisticsRequest {
  int64 organization_id = 1;
  string start_date = 2;
  string end_date = 3;
  string period = 4; // "DAY", "WEEK", "MONTH"
}

message GetPopularItemsRequest {
  int64 organization_id = 1;
  string start_date = 2;
  string end_date = 3;
  int32 limit = 4; // Number of top items to return
}

message OrderStatistics {
  int64 total_orders = 1;
  double total_revenue = 2;
  double average_order_value = 3;
  int64 pending_orders = 4;
  int64 completed_orders = 5;
  int64 cancelled_orders = 6;
  repeated DailyStatistics daily_stats = 7;
}

message DailyStatistics {
  string date = 1;
  int64 orders_count = 2;
  double revenue = 3;
  double average_order_value = 4;
}

message PopularItem {
  int64 item_id = 1;
  string item_name = 2;
  int64 order_count = 3;
  int64 total_quantity = 4;
  double total_revenue = 5;
}

// Response Messages
message OrderResponse {
  bool success = 1;
  string message = 2;
  Order order = 3;
}

message ListOrdersResponse {
  bool success = 1;
  string message = 2;
  repeated Order orders = 3;
  int64 total_count = 4;
  int32 page = 5;
  int32 size = 6;
}

message OrderItemResponse {
  bool success = 1;
  string message = 2;
  OrderItem order_item = 3;
}

message OrderStatisticsResponse {
  bool success = 1;
  string message = 2;
  OrderStatistics statistics = 3;
}

message PopularItemsResponse {
  bool success = 1;
  string message = 2;
  repeated PopularItem popular_items = 3;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
