syntax = "proto3";

package com.example.attendancesystem.grpc.table;

option java_multiple_files = true;
option java_package = "com.example.attendancesystem.grpc.table";
option java_outer_classname = "TableServiceProto";

// Table Management Service
service TableService {
  // Table CRUD Operations
  rpc CreateTable(CreateTableRequest) returns (TableResponse);
  rpc GetTable(GetTableRequest) returns (TableResponse);
  rpc UpdateTable(UpdateTableRequest) returns (TableResponse);
  rpc DeleteTable(DeleteTableRequest) returns (DeleteResponse);
  rpc ListTables(ListTablesRequest) returns (ListTablesResponse);
  
  // Table Status Management
  rpc UpdateTableStatus(UpdateTableStatusRequest) returns (TableResponse);
  rpc GetAvailableTables(GetAvailableTablesRequest) returns (ListTablesResponse);
  rpc GetTablesByStatus(GetTablesByStatusRequest) returns (ListTablesResponse);
  
  // QR Code Management
  rpc GenerateTableQrCode(GenerateTableQrCodeRequest) returns (QrCodeResponse);
  rpc GetTableQrCode(GetTableQrCodeRequest) returns (QrCodeResponse);
  rpc ValidateTableQrCode(ValidateTableQrCodeRequest) returns (QrCodeValidationResponse);
  rpc RegenerateTableQrCode(RegenerateTableQrCodeRequest) returns (QrCodeResponse);
  
  // Table Reservations (if needed)
  rpc ReserveTable(ReserveTableRequest) returns (TableResponse);
  rpc ReleaseTable(ReleaseTableRequest) returns (TableResponse);
}

// Table Messages
message RestaurantTable {
  int64 id = 1;
  string table_number = 2;
  int32 capacity = 3;
  string location = 4;
  string status = 5; // "AVAILABLE", "OCCUPIED", "RESERVED", "OUT_OF_SERVICE"
  int64 organization_id = 6;
  bool active = 7;
  string created_at = 8;
  string updated_at = 9;
  string qr_code_data = 10;
  string qr_code_url = 11;
  string description = 12;
  double x_position = 13; // For floor plan positioning
  double y_position = 14; // For floor plan positioning
  string current_order_id = 15; // Current active order if any
}

message CreateTableRequest {
  string table_number = 1;
  int32 capacity = 2;
  string location = 3;
  int64 organization_id = 4;
  string description = 5;
  double x_position = 6;
  double y_position = 7;
}

message GetTableRequest {
  int64 id = 1;
}

message UpdateTableRequest {
  int64 id = 1;
  string table_number = 2;
  int32 capacity = 3;
  string location = 4;
  bool active = 5;
  string description = 6;
  double x_position = 7;
  double y_position = 8;
}

message DeleteTableRequest {
  int64 id = 1;
}

message ListTablesRequest {
  int64 organization_id = 1;
  int32 page = 2;
  int32 size = 3;
  string search = 4;
  bool active_only = 5;
  string location = 6; // Optional filter by location
  int32 min_capacity = 7; // Optional filter by minimum capacity
}

// Table Status Messages
message UpdateTableStatusRequest {
  int64 id = 1;
  string status = 2;
  string current_order_id = 3; // Optional
}

message GetAvailableTablesRequest {
  int64 organization_id = 1;
  int32 min_capacity = 2; // Optional minimum capacity requirement
  string location = 3; // Optional location filter
}

message GetTablesByStatusRequest {
  int64 organization_id = 1;
  string status = 2;
  int32 page = 3;
  int32 size = 4;
}

// QR Code Messages
message GenerateTableQrCodeRequest {
  int64 table_id = 1;
  string base_url = 2; // Base URL for the menu system
  bool regenerate = 3; // Force regeneration even if exists
}

message GetTableQrCodeRequest {
  int64 table_id = 1;
}

message ValidateTableQrCodeRequest {
  string qr_code_data = 1;
}

message RegenerateTableQrCodeRequest {
  int64 table_id = 1;
  string base_url = 2;
}

// Reservation Messages
message ReserveTableRequest {
  int64 table_id = 1;
  string customer_name = 2;
  string customer_phone = 3;
  string reservation_time = 4;
  int32 party_size = 5;
  string special_requests = 6;
}

message ReleaseTableRequest {
  int64 table_id = 1;
  string reason = 2; // "ORDER_COMPLETED", "CUSTOMER_LEFT", "MANUAL_RELEASE"
}

// Response Messages
message TableResponse {
  bool success = 1;
  string message = 2;
  RestaurantTable table = 3;
}

message ListTablesResponse {
  bool success = 1;
  string message = 2;
  repeated RestaurantTable tables = 3;
  int64 total_count = 4;
  int32 page = 5;
  int32 size = 6;
}

message QrCodeResponse {
  bool success = 1;
  string message = 2;
  string qr_code_data = 3;
  bytes qr_code_image = 4;
  string qr_code_url = 5;
  int64 table_id = 6;
  string table_number = 7;
}

message QrCodeValidationResponse {
  bool valid = 1;
  string message = 2;
  int64 table_id = 3;
  string table_number = 4;
  int64 organization_id = 5;
  string menu_url = 6;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}

// Table Statistics (for analytics)
message TableStatistics {
  int64 table_id = 1;
  string table_number = 2;
  int64 total_orders = 3;
  double total_revenue = 4;
  double average_order_value = 5;
  int32 average_occupancy_time = 6; // in minutes
  string most_popular_time_slot = 7;
}

message GetTableStatisticsRequest {
  int64 organization_id = 1;
  string start_date = 2;
  string end_date = 3;
  int64 table_id = 4; // Optional, for specific table stats
}

message TableStatisticsResponse {
  bool success = 1;
  string message = 2;
  repeated TableStatistics table_statistics = 3;
  int64 total_tables = 4;
  double overall_occupancy_rate = 5;
}
