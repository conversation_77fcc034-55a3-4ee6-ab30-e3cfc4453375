syntax = "proto3";

package com.example.attendancesystem.grpc.menu;

option java_multiple_files = true;
option java_package = "com.example.attendancesystem.grpc.menu";
option java_outer_classname = "MenuServiceProto";

// Menu Management Service
service MenuService {
  // Category Management
  rpc CreateCategory(CreateCategoryRequest) returns (CategoryResponse);
  rpc GetCategory(GetCategoryRequest) returns (CategoryResponse);
  rpc UpdateCategory(UpdateCategoryRequest) returns (CategoryResponse);
  rpc DeleteCategory(DeleteCategoryRequest) returns (DeleteResponse);
  rpc ListCategories(ListCategoriesRequest) returns (ListCategoriesResponse);
  
  // Item Management
  rpc CreateItem(CreateItemRequest) returns (ItemResponse);
  rpc GetItem(GetItemRequest) returns (ItemResponse);
  rpc UpdateItem(UpdateItemRequest) returns (ItemResponse);
  rpc DeleteItem(DeleteItemRequest) returns (DeleteResponse);
  rpc ListItems(ListItemsRequest) returns (ListItemsResponse);
  rpc ListItemsByCategory(ListItemsByCategoryRequest) returns (ListItemsResponse);
  
  // Public Menu Access (for customers)
  rpc GetPublicMenu(GetPublicMenuRequest) returns (PublicMenuResponse);
  rpc GetPublicCategory(GetPublicCategoryRequest) returns (CategoryResponse);
  rpc GetPublicItem(GetPublicItemRequest) returns (ItemResponse);
  rpc SearchMenuItems(SearchMenuItemsRequest) returns (ListItemsResponse);
}

// Category Messages
message Category {
  int64 id = 1;
  string name = 2;
  string description = 3;
  int64 organization_id = 4;
  bool active = 5;
  int32 sort_order = 6;
  string image_url = 7;
  string created_at = 8;
  string updated_at = 9;
  repeated Item items = 10;
}

message CreateCategoryRequest {
  string name = 1;
  string description = 2;
  int64 organization_id = 3;
  int32 sort_order = 4;
  string image_url = 5;
}

message GetCategoryRequest {
  int64 id = 1;
}

message UpdateCategoryRequest {
  int64 id = 1;
  string name = 2;
  string description = 3;
  bool active = 4;
  int32 sort_order = 5;
  string image_url = 6;
}

message DeleteCategoryRequest {
  int64 id = 1;
}

message ListCategoriesRequest {
  int64 organization_id = 1;
  int32 page = 2;
  int32 size = 3;
  string search = 4;
  bool active_only = 5;
}

// Item Messages
message Item {
  int64 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  int64 category_id = 5;
  int64 organization_id = 6;
  bool available = 7;
  bool active = 8;
  int32 sort_order = 9;
  string image_url = 10;
  string ingredients = 11;
  string allergens = 12;
  int32 preparation_time = 13; // in minutes
  string created_at = 14;
  string updated_at = 15;
  Category category = 16;
}

message CreateItemRequest {
  string name = 1;
  string description = 2;
  double price = 3;
  int64 category_id = 4;
  int64 organization_id = 5;
  int32 sort_order = 6;
  string image_url = 7;
  string ingredients = 8;
  string allergens = 9;
  int32 preparation_time = 10;
}

message GetItemRequest {
  int64 id = 1;
}

message UpdateItemRequest {
  int64 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  int64 category_id = 5;
  bool available = 6;
  bool active = 7;
  int32 sort_order = 8;
  string image_url = 9;
  string ingredients = 10;
  string allergens = 11;
  int32 preparation_time = 12;
}

message DeleteItemRequest {
  int64 id = 1;
}

message ListItemsRequest {
  int64 organization_id = 1;
  int32 page = 2;
  int32 size = 3;
  string search = 4;
  bool available_only = 5;
  bool active_only = 6;
}

message ListItemsByCategoryRequest {
  int64 category_id = 1;
  int32 page = 2;
  int32 size = 3;
  bool available_only = 4;
  bool active_only = 5;
}

// Public Menu Messages
message GetPublicMenuRequest {
  int64 organization_id = 1;
  bool available_only = 2;
}

message GetPublicCategoryRequest {
  int64 id = 1;
  bool available_only = 2;
}

message GetPublicItemRequest {
  int64 id = 1;
}

message SearchMenuItemsRequest {
  int64 organization_id = 1;
  string search_query = 2;
  int32 page = 3;
  int32 size = 4;
  bool available_only = 5;
  double min_price = 6;
  double max_price = 7;
  repeated int64 category_ids = 8;
}

message PublicMenuResponse {
  bool success = 1;
  string message = 2;
  repeated Category categories = 3;
  string organization_name = 4;
}

// Response Messages
message CategoryResponse {
  bool success = 1;
  string message = 2;
  Category category = 3;
}

message ListCategoriesResponse {
  bool success = 1;
  string message = 2;
  repeated Category categories = 3;
  int64 total_count = 4;
  int32 page = 5;
  int32 size = 6;
}

message ItemResponse {
  bool success = 1;
  string message = 2;
  Item item = 3;
}

message ListItemsResponse {
  bool success = 1;
  string message = 2;
  repeated Item items = 3;
  int64 total_count = 4;
  int32 page = 5;
  int32 size = 6;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}
