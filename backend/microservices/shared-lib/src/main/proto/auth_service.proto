syntax = "proto3";

package com.example.attendancesystem.grpc.auth;

option java_multiple_files = true;
option java_package = "com.example.attendancesystem.grpc.auth";
option java_outer_classname = "AuthServiceProto";

// Authentication and Authorization Service
service AuthService {
  // Entity Admin Authentication
  rpc AuthenticateEntityAdmin(EntityAdminLoginRequest) returns (AuthResponse);
  rpc RefreshEntityAdminToken(RefreshTokenRequest) returns (AuthResponse);
  rpc LogoutEntityAdmin(LogoutRequest) returns (LogoutResponse);
  
  // Super Admin Authentication
  rpc AuthenticateSuperAdmin(SuperAdminLoginRequest) returns (AuthResponse);
  rpc RefreshSuperAdminToken(RefreshTokenRequest) returns (AuthResponse);
  rpc LogoutSuperAdmin(LogoutRequest) returns (LogoutResponse);
  
  // Subscriber Authentication
  rpc AuthenticateSubscriber(SubscriberLoginRequest) returns (AuthResponse);
  rpc RefreshSubscriberToken(RefreshTokenRequest) returns (AuthResponse);
  rpc LogoutSubscriber(LogoutRequest) returns (LogoutResponse);
  
  // Token Validation
  rpc ValidateToken(TokenValidationRequest) returns (TokenValidationResponse);
  rpc BlacklistToken(BlacklistTokenRequest) returns (BlacklistTokenResponse);

  // Entity Admin Management
  rpc CreateEntityAdminForAuth(CreateEntityAdminForAuthRequest) returns (CreateEntityAdminForAuthResponse);

  // Password Management
  rpc HashPassword(HashPasswordRequest) returns (HashPasswordResponse);
}

// Request Messages
message EntityAdminLoginRequest {
  string username = 1;
  string password = 2;
  int64 organization_id = 3;
}

message SuperAdminLoginRequest {
  string username = 1;
  string password = 2;
}

message SubscriberLoginRequest {
  string username = 1;
  string password = 2;
}

message RefreshTokenRequest {
  string refresh_token = 1;
  string user_type = 2; // "ENTITY_ADMIN", "SUPER_ADMIN", "SUBSCRIBER"
}

message LogoutRequest {
  string access_token = 1;
  string refresh_token = 2;
  string user_type = 3;
}

message TokenValidationRequest {
  string token = 1;
  string user_type = 2;
}

message BlacklistTokenRequest {
  string token = 1;
  string user_type = 2;
}

// Response Messages
message AuthResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
  int64 expires_in = 5;
  UserInfo user_info = 6;
}

message LogoutResponse {
  bool success = 1;
  string message = 2;
}

message TokenValidationResponse {
  bool valid = 1;
  string message = 2;
  UserInfo user_info = 3;
}

message BlacklistTokenResponse {
  bool success = 1;
  string message = 2;
}

// Common Messages
message UserInfo {
  int64 id = 1;
  string username = 2;
  string user_type = 3;
  int64 organization_id = 4;
  repeated string roles = 5;
  repeated string permissions = 6;
}

// Error Handling
message ErrorDetails {
  string code = 1;
  string message = 2;
  repeated string details = 3;
}

// Entity Admin Management Messages
message CreateEntityAdminForAuthRequest {
  string username = 1;
  string password = 2;
  int64 organization_id = 3;
}

message CreateEntityAdminForAuthResponse {
  bool success = 1;
  string message = 2;
  int64 entity_admin_id = 3;
}

message HashPasswordRequest {
  string password = 1;
}

message HashPasswordResponse {
  bool success = 1;
  string message = 2;
  string hashed_password = 3;
}
