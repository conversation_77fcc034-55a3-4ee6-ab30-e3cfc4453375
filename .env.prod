# Production Environment Configuration for Attendance Management System
# Copy this file to .env and update values for your production environment

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Application Security
JWT_SECRET=your_very_secure_jwt_secret_key_minimum_32_characters_long

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# Monitoring
GRAFANA_PASSWORD=your_secure_grafana_admin_password

# Application Configuration
REGION=us-east-1
APP_BASE_URL=https://your-domain.com

# SSL Configuration (if using HTTPS)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_MAX_SIZE=50m
LOG_MAX_FILES=5

# Performance Tuning
JAVA_HEAP_SIZE=2g
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB

# Network Configuration
NETWORK_SUBNET=172.21.0.0/16

# Service Discovery
SERVICE_DISCOVERY_ENABLED=true
SERVICE_DISCOVERY_ENVIRONMENT=production
